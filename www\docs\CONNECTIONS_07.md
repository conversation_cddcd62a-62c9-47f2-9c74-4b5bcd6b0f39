# GPS Pokemon App - Component Connections & Dependencies

*This document maps the interconnections between components. For basic file descriptions, see OVERVIEW_07.md.*

## Critical Dependency Chains

### Application Initialization Flow
**main.js** → **gameState.initialize()** → **pokemonManager.initialize()** → **MapRenderer.initMap()** → **PokemonSpawner** → **FabManager.initialize()**

### Data Flow Dependencies
**Pokemon Creation:** pokemon-spawner.js → Pokemon.js → pokemonManager.addPokemon() → storage-service.set()
**Battle System:** BattleScreen/TrainerBattleScreen → battle-calc.js → pokemonManager.updatePokemon() → storage-service.set()
**UI Navigation:** FabManager → FabSubmenuManager → Screen Components → capacitor/app.js (back button)

## Function-Level Dependencies

### BattleScreen.js Critical Functions
- `calculateExpProgress()` (lines 303-376) - **INCONSISTENCY:** Internal implementation instead of battle-utils.js
- `applyFlyingOffset()` - Depends on gameState.pokedexData for flying_offset values
- `handleBattleResult()` - Calls pokemonManager.updatePokemon(), triggers level-up notifications

**Dependency Chain:** BattleScreen → battle-calc.js → experience-system.js → pokemonManager → storage-service

### TrainerBattleScreen.js Critical Functions
- `awardRoundXP()` - Uses battle-utils.js `calculateExpProgress()` (line 19 import)
- `selectPokemonForRound()` - Depends on teamStorage.loadTeam() data
- `handleBattleComplete()` - Updates multiple Pokemon through pokemonManager

**Dependency Chain:** TrainerBattleScreen → battle-utils.js → pokemonManager → storage-service

### PokemonCaughtScreen.js Critical Functions
- `loadData()` - Sequential dependency: pokemonManager.initialize() → loadTeam() → JSON fetches
- `handleTeamAction()` - Calls teamStorage functions → affects battle screen Pokemon availability

**Dependency Chain:** PokemonCaughtScreen → pokemonManager → teamStorage → storage-service

## Service Layer Critical Connections

### pokemon-manager.js - Central Data Hub
**Critical Methods:**
- `initialize()` → Calls Pokemon.fromJSON() → Validates experience → Migrates old storage
- `getCaughtPokemon()` → Used by PokemonCaughtScreen, PokedexScreen
- `addPokemon()` → Called after battles, affects ALL Pokemon-displaying screens
- `updatePokemon()` → Called by battle screens, must sync with storage immediately

**Dependency Risk:** ALL screens depend on this service - failures cascade throughout app

### pokemon-spawner.js - Location-Based Generation
**Critical Methods:**
- `spawnPokemon()` → Depends on gameState.pokedexData being loaded first
- `recreateStoredPokemons()` → Called by main.js time-events, must handle corrupted storage data
- `checkForSpawns()` → Triggers based on location changes, affects map display

**Synchronization Point:** Must coordinate with time-events.js for spawn persistence

### map-renderer.js - Visual State Manager
**Critical Methods:**
- `renderPokemons()` → Must sync with gameState.pokemons array changes
- `renderTrainers()` → Must sync with gameState.trainers array changes
- Marker click handlers → Trigger screen transitions, must clean up properly

**Performance Risk:** Large numbers of markers can impact mobile performance

## Critical Synchronization Points

### storage-service.js - Single Point of Failure
**Risk:** ALL data persistence flows through this service
**Dependencies:** Capacitor detection → Platform-specific storage → JSON serialization
**Failure Impact:** Complete data loss, app becomes unusable

### game-state.js - Central State Bottleneck
**Critical State:**
- `pokemons[]` - Modified by spawner, read by map-renderer, battle screens
- `pokedexData[]` - Must be loaded before Pokemon creation/display
- `map` reference - Used by all location-based features
- Event system - Components rely on state change notifications

**Synchronization Risk:** State changes must propagate to all dependent components

## Utility Function Critical Dependencies

### battle-utils.js vs BattleScreen.js Inconsistency
**Issue:** Two different XP calculation implementations
- **TrainerBattleScreen** uses `battle-utils.calculateExpProgress()`
- **BattleScreen** has internal `calculateExpProgress()` method (lines 303-376)
**Risk:** XP calculations may differ between battle types

### pokemon-display-names.js - Data Consistency Guardian
**Critical Function:** `capitalizePokemonName()` prevents JSON parsing errors
**Used By:** ALL components displaying Pokemon names
**Failure Impact:** UI display errors, data corruption in storage

## Critical Connection Points

### Data Flow Bottlenecks
1. **pokemonManager** - All Pokemon data flows through this service
2. **gameState** - Central state container for all components
3. **storage-service** - All persistence operations go through this service

### UI Navigation Flow
1. **main.js** → **FabManager** → **FabSubmenuManager** → Individual screens
2. **Back button handling** → **capacitor/app.js** → Screen-specific handlers

### Battle System Flow
1. **Map Pokemon click** → **BattleScreen** → **battle-calc.js** → **pokemonManager.updatePokemon()**
2. **Trainer marker click** → **TrainerBattleScreen** → **battle-calc.js** → **pokemonManager** updates

## Identified Issues During Analysis

### Experience System Inconsistency
- **BattleScreen.js** has internal `calculateExpProgress()` method (lines 303-376)
- **TrainerBattleScreen.js** imports `calculateExpProgress` from battle-utils.js
- **Recommendation:** Standardize on battle-utils.js implementation for consistency

### Potential Circular Dependencies
- **pokemon-manager.js** ↔ **Pokemon.js** mutual imports
- **gameState** referenced by most services that gameState also imports
- **Dynamic imports** used in several places to avoid circular dependencies

### Data Loading Redundancy
- **pokedex-151.json** loaded multiple times by different components
- Should be centralized through gameState.pokedexData

## Component Modification Impact Map

### When modifying Pokemon data structure:
**Affected:** Pokemon.js → pokemonManager → ALL screens → storage-service → battle-calc.js

### When modifying battle system:
**Affected:** battle-calc.js → BattleScreen.js → TrainerBattleScreen.js → battle-utils.js → experience-system.js

### When modifying UI navigation:
**Affected:** FabManager → FabSubmenuManager → ALL screen components → capacitor/app.js

### When modifying map functionality:
**Affected:** map-renderer.js → pokemon-spawner.js → main.js → gameState → ALL location-dependent features

## Detailed Function Dependencies

### Core Service Functions

#### pokemon-manager.js Key Methods
- `initialize()` → Calls `Pokemon.fromJSON()`, `storageService.get()`, `validatePokemonExperience()`
- `getCaughtPokemon()` → Used by PokemonCaughtScreen, PokedexScreen
- `addPokemon()` → Called after successful battles, Pokemon catches
- `updatePokemon()` → Called by battle screens after XP/level changes
- `getTeamPokemon()` → Used by battle screens for player Pokemon data

#### pokemon-spawner.js Key Methods
- `spawnPokemon()` → Calls `Pokemon` constructor, `updatePokemonFromPokedex()`, `calculateSpawnLevel()`
- `recreateStoredPokemons()` → Used by main.js time-events system
- `checkForSpawns()` → Called by main.js location change handlers
- **Dependencies:** gameState.pokemons, gameState.pokedexData, pokemon-grid.js functions

#### map-renderer.js Key Methods
- `renderPokemons()` → Reads gameState.pokemons, creates Leaflet markers
- `renderTrainers()` → Reads gameState.trainers, creates trainer markers
- `updateDebugInfo()` → Displays debug overlays when gameState.debugMode is true
- **Dependencies:** gameState for all data, LANDUSE_TYPE_MAPPING for special markers

### Screen Component Functions

#### BattleScreen.js Key Methods
- `render()` → Creates battle UI, calls `applyFlyingOffset()`, `updateTypeBadges()`
- `calculateExpProgress()` → Internal XP calculation (INCONSISTENT with TrainerBattleScreen)
- `handleBattleResult()` → Calls `pokemonManager.updatePokemon()`, triggers level-up notifications
- `cleanup()` → Removes event listeners, calls onBattleComplete callback

#### TrainerBattleScreen.js Key Methods
- `render()` → Creates trainer battle UI, loads trainer data
- `awardRoundXP()` → Uses `calculateExpProgress()` from battle-utils.js
- `selectPokemonForRound()` → Interfaces with team Pokemon selection
- `handleBattleComplete()` → Updates multiple Pokemon through pokemonManager

#### PokemonCaughtScreen.js Key Methods
- `loadData()` → Calls `pokemonManager.initialize()`, `loadTeam()`, fetches JSON files
- `handleTeamAction()` → Calls `addToTeam()`, `removeFromTeam()`, `makePokemonBuddy()`
- `findPokemonByName()` → Searches pokedexData for Pokemon details
- **Critical:** Manages starter Pokemon integration with team system

### Storage Layer Functions

#### storage-service.js Methods
- `get()` → Used by ALL components for data retrieval
- `set()` → Used by ALL components for data persistence
- **Platform Detection:** Automatically switches between Capacitor Storage and localStorage

#### teamStorage.js Functions
- `loadTeam()` → Used by PokemonCaughtScreen, battle screens
- `addToTeam()` → Called from PokemonCaughtScreen team management
- `removeFromTeam()` → Called from PokemonCaughtScreen team management
- `makePokemonBuddy()` → Sets first team position for battle system

### Utility Function Dependencies

#### battle-utils.js Functions
- `calculateExpProgress()` → Used by TrainerBattleScreen (NOT by BattleScreen - inconsistency)
- `updateExpBar()` → Shared XP bar rendering logic
- `updateTypeBadges()` → Shared type badge display logic
- **Should be used consistently across all battle-related components**

#### pokemon-display-names.js Functions
- `getGermanPokemonName()` → Used by ALL screens displaying Pokemon names
- `capitalizePokemonName()` → Critical for data consistency, prevents parsing errors

## Event Flow Chains

### Pokemon Catch Flow
1. **Map Pokemon click** → BattleScreen.render()
2. **Battle completion** → BattleScreen.handleBattleResult()
3. **Pokemon caught** → pokemonManager.addPokemon()
4. **Storage update** → storage-service.set()
5. **UI update** → gameState.pokedex update → PokedexScreen refresh

### Team Management Flow
1. **FAB submenu** → PokemonCaughtScreen.render()
2. **Team action** → PokemonCaughtScreen.handleTeamAction()
3. **Storage update** → teamStorage functions
4. **Battle system** → Updated team available for battles

### Location-Based Spawning Flow
1. **GPS update** → main.js location handlers
2. **Distance check** → pokemon-spawner.js checkForSpawns()
3. **Spawn generation** → pokemon-spawner.js spawnPokemon()
4. **Map update** → map-renderer.js renderPokemons()
5. **Storage persistence** → time-events.js spawn storage

## Critical Synchronization Points

### Data Consistency Requirements
1. **Pokemon Experience:** Must sync between pokemonManager, battle screens, and storage
2. **Team State:** Must sync between teamStorage, PokemonCaughtScreen, and battle screens
3. **Pokedex Data:** Must be loaded before Pokemon creation/display operations
4. **Map State:** Pokemon markers must reflect current gameState.pokemons array

### Error Propagation Paths
1. **Storage failures** → Affect ALL components through storage-service
2. **Pokemon data corruption** → Affects pokemonManager → ALL screens
3. **GPS failures** → Affect spawning → Map display → Battle availability
4. **JSON loading failures** → Affect pokedex data → Pokemon creation → UI display

## Architecture Recommendations

### Immediate Issues to Address
1. **Standardize XP calculation** - Use battle-utils.js consistently
2. **Centralize pokedex loading** - Avoid multiple JSON fetches
3. **Implement proper cleanup** - Ensure event listeners are removed
4. **Add error boundaries** - Prevent cascading failures

### Long-term Improvements
1. **Dependency injection** - Reduce circular dependencies
2. **Event-driven architecture** - Reduce tight coupling between components
3. **State management library** - Consider Redux-like solution for complex state
4. **Component lifecycle management** - Standardize initialization/cleanup patterns
